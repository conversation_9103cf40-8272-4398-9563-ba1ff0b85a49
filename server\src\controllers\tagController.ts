import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { Op } from 'sequelize';
import Tag from '../models/Tag';
import { ErrorResponse } from '../middleware/error';
import { ITag } from '../models/Tag';

// @desc    获取所有标签
// @route   GET /api/tags
// @access  Public
export const getTags = async (req: Request, res: Response) => {
  try {
    const tags = await Tag.findAll({
      order: [['name', 'ASC']]
    });

    res.json({
      code: 0,
      message: '获取标签列表成功',
      data: tags
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '获取标签列表失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// @desc    获取单个标签
// @route   GET /api/tags/:id
// @access  Public
export const getTag = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tag = await Tag.findByPk(id);

    if (!tag) {
      return res.status(404).json({
        code: 404,
        message: '标签不存在'
      });
    }

    res.json({
      code: 0,
      message: '获取标签成功',
      data: tag
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '获取标签失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// @desc    创建标签
// @route   POST /api/tags
// @access  Private/Admin
export const createTag = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '输入数据验证失败',
        errors: errors.array()
      });
    }

    const { name, description, slug } = req.body;

    // 检查标签名是否已存在
    const existingTag = await Tag.findOne({ 
      where: { name } 
    });
    
    if (existingTag) {
      return res.status(400).json({
        code: 400,
        message: '标签名称已存在'
      });
    }

    // 生成或验证slug
    let finalSlug = slug || name.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, '-');
    
    // 检查slug是否已存在
    let slugExists = await Tag.findOne({ 
      where: { slug: finalSlug } 
    });
    
    let counter = 1;
    while (slugExists) {
      finalSlug = `${slug || name.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, '-')}-${counter}`;
      slugExists = await Tag.findOne({ 
        where: { slug: finalSlug } 
      });
      counter++;
    }

    const tag = await Tag.create({
      name,
      description,
      slug: finalSlug
    });

    res.status(201).json({
      code: 201,
      message: '创建标签成功',
      data: tag
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '创建标签失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// @desc    更新标签
// @route   PUT /api/tags/:id
// @access  Private/Admin
export const updateTag = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const [affectedCount] = await Tag.update(updates, {
      where: { id }
    });

    if (affectedCount === 0) {
      return res.status(404).json({
        code: 404,
        message: '标签不存在'
      });
    }

    const updatedTag = await Tag.findByPk(id);

    res.json({
      code: 0,
      message: '更新标签成功',
      data: updatedTag
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '更新标签失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// @desc    删除标签
// @route   DELETE /api/tags/:id
// @access  Private/Admin
export const deleteTag = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const tag = await Tag.findByPk(id);
    if (!tag) {
      return res.status(404).json({
        code: 404,
        message: '标签不存在'
      });
    }

    await tag.destroy();

    res.json({
      code: 0,
      message: '删除标签成功'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '删除标签失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// @desc    根据slug获取标签
// @route   GET /api/tags/slug/:slug
// @access  Public
export const getTagBySlug = async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const tag = await Tag.findOne({
      where: { slug }
    });

    if (!tag) {
      return res.status(404).json({
        code: 404,
        message: '标签不存在'
      });
    }

    res.json({
      code: 0,
      message: '获取标签成功',
      data: tag
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '获取标签失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// @desc    搜索标签
// @route   GET /api/tags/search
// @access  Public
export const searchTags = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    
    if (!q) {
      return res.status(400).json({
        code: 400,
        message: '请提供搜索关键词'
      });
    }

    const tags = await Tag.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.like]: `%${q}%` } },
          { description: { [Op.like]: `%${q}%` } }
        ]
      },
      order: [['name', 'ASC']],
      limit: 20
    });

    res.json({
      code: 0,
      message: '搜索标签成功',
      data: tags
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '搜索标签失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};
