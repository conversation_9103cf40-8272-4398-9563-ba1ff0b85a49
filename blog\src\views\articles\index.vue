<template>
  <div class="articles-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">
          <n-gradient-text type="primary">技术文档</n-gradient-text>
        </h1>
        <p class="page-desc">探索和学习各种技术主题</p>
      </div>
    </div>

    <!-- 顶部标签筛选 -->
    <div class="tags-section">
      <div class="container">
        <div class="tags-wrapper">
          <span class="tags-label">标签筛选：</span>
          <n-space wrap :size="12">
            <n-tag
              v-for="tag in tags"
              :key="tag._id || tag.id"
              :type="selectedTags.includes(tag._id || tag.id) ? 'primary' : 'default'"
              :bordered="false"
              size="medium"
              round
              clickable
              @click="handleTagClick(tag._id || tag.id)"
            >
              <template #icon>
                <n-icon><PricetagOutline /></n-icon>
              </template>
              {{ tag.name }} ({{ getTagArticleCount(tag._id || tag.id) }})
            </n-tag>
          </n-space>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <n-grid :cols="24" :x-gap="24">
        <!-- 左侧导航 -->
        <n-grid-item :span="6">
          <n-card class="nav-card" :bordered="false">
            <div class="nav-header">
              <n-gradient-text type="primary" :size="18">文档导航</n-gradient-text>
            </div>
            <n-collapse accordion class="custom-collapse" @update:value="handleCollapseChange">
              <n-collapse-item
                v-for="category in categories"
                :key="category.id"
                :title="category.name"
                :name="category.id.toString()"
              >
                <template #header>
                  <div class="collapse-header">
                    <n-icon size="18"><FolderOutline /></n-icon>
                    <span>{{ category.name }}</span>
                    <span class="article-count">({{ getCategoryArticleCount(category._id || category.id) }})</span>
                  </div>
                </template>
                <div class="nav-articles">
                  <div
                    v-for="article in getFilteredArticles(category._id || category.id)"
                    :key="article._id || article.id"
                    class="nav-item"
                    :class="{ active: selectedArticle?._id === article._id || selectedArticle?.id === article.id }"
                    @click="handleArticleSelect(article)"
                  >
                    <div class="nav-item-content">
                      <n-icon size="14"><DocumentTextOutline /></n-icon>
                      <span class="nav-item-title">{{ article.title }}</span>
                    </div>
                  </div>
                  <div v-if="getFilteredArticles(category._id || category.id).length === 0" class="empty-message">
                    该分类下暂无相关文章
                  </div>
                </div>
              </n-collapse-item>
            </n-collapse>
          </n-card>
        </n-grid-item>

        <!-- 右侧内容 -->
        <n-grid-item :span="18">
          <n-card class="content-card" :bordered="false" v-if="selectedArticle">
            <div class="article-content">
              <div class="article-header">
                <h1 class="article-title">{{ selectedArticle.title }}</h1>
                <div class="article-meta">
                  <div class="meta-left">
                    <n-avatar
                      :size="40"
                      round
                      :src="selectedArticle.author?.avatar"
                    >
                      {{ selectedArticle.author?.nickname?.charAt(0) || selectedArticle.author?.username?.charAt(0) }}
                    </n-avatar>
                    <div class="author-info">
                      <div class="author-name">{{ selectedArticle.author?.nickname || selectedArticle.author?.username }}</div>
                      <div class="post-info">
                        <span>{{ formatDate(selectedArticle.created_at) }}</span>
                        <span class="dot">·</span>
                        <span>{{ formatNumber(selectedArticle.view_count) }} 次阅读</span>
                      </div>
                    </div>
                  </div>
                  <div class="meta-right">
                    <n-space>
                      <n-tag v-if="selectedArticle.category" type="primary" :bordered="false">
                        {{ selectedArticle.category.name }}
                      </n-tag>
                      <n-tag 
                        v-for="tag in selectedArticle.tags" 
                        :key="tag._id"
                        type="success"
                        :bordered="false"
                      >
                        {{ tag.name }}
                      </n-tag>
                    </n-space>
                  </div>
                </div>
              </div>

              <!-- 文章封面图 -->
              <div v-if="selectedArticle.cover_image" class="article-cover">
                <img :src="selectedArticle.cover_image" :alt="selectedArticle.title">
              </div>

              <!-- 文章内容 -->
              <div class="article-body">
                <div v-html="selectedArticle.content" class="rich-content"></div>
              </div>
            </div>
          </n-card>
          <n-empty 
            v-else 
            class="empty-state"
            description="请选择一篇文章"
          >
            <template #icon>
              <n-icon size="100" color="var(--n-text-color-disabled)">
                <DocumentTextOutline />
              </n-icon>
            </template>
          </n-empty>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NCard,
  NGrid,
  NGridItem,
  NSpace,
  NTag,
  NIcon,
  NCollapse,
  NCollapseItem,
  NEmpty,
  NGradientText,
  NAvatar,
  useMessage
} from 'naive-ui'
import {
  Eye,
  FolderOutline,
  DocumentTextOutline,
  TimeOutline,
  PricetagOutline
} from '@vicons/ionicons5'
import { articleApi, categoryApi, tagApi } from '@/api'
import { formatDate, formatNumber } from '@/utils/format'
import type { Article, Tag, ArticleQuery } from '@/types/api'

const router = useRouter()
const route = useRoute()
const message = useMessage()

// 状态
const loading = ref(false)
const categories = ref<any[]>([])
const tags = ref<any[]>([])
const selectedTags = ref<(number | string)[]>([])
const categoryArticles = ref<Record<number | string, any[]>>({})
const selectedArticle = ref<any>(null)

const query: ArticleQuery = {
  page: 1,
  limit: 100,
  order: '-created_at',
  tag: selectedTags.value.join(',')
}

// 获取分类下的文章
const fetchCategoryArticles = async (categoryId: number | string) => {
  try {
    loading.value = true
    const res = await articleApi.getArticles({
      category: categoryId,
      tags: selectedTags.value,
      limit: 1000,  // 增加限制数量
      status: 'published'  // 只获取已发布的文章
    })
    console.log('获取分类文章响应:', res)
    if (res.data?.code === 0 || res.data?.code === 200) {
      categoryArticles.value[categoryId] = res.data.data.items || []
      console.log('分类文章数量:', categoryArticles.value[categoryId].length)
    }
  } catch (error) {
    console.error('获取分类文章失败:', error)
    message.error('获取分类文章失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await categoryApi.getCategories()
    console.log('获取分类响应:', res)
    if (res.data?.code === 0 || res.data?.code === 200) {
      categories.value = res.data.data
      // 获取所有分类的文章
      await Promise.all(
        categories.value.map(category => fetchCategoryArticles(category.id || category._id))
      )
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 获取标签列表
const fetchTags = async () => {
  try {
    const res = await tagApi.getTags()
    console.log('获取标签响应:', res)
    if (res.data?.code === 0 || res.data?.code === 200) {
      tags.value = res.data.data
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}

// 添加计算属性来过滤文章
const getFilteredArticles = (categoryId: number | string) => {
  const articles = categoryArticles.value[categoryId] || []
  if (selectedTags.value.length === 0) {
    return articles
  }
  return articles.filter(article => {
    return article.tags.some(tag => selectedTags.value.includes(tag.id || tag._id))
  })
}

// 添加计算属性来获取分类下的文章数量
const getCategoryArticleCount = (categoryId: number | string) => {
  const articles = getFilteredArticles(categoryId)
  return articles.length
}

// 添加计算标签文章数量的函数
const getTagArticleCount = (tagId: number | string) => {
  let count = 0
  Object.values(categoryArticles.value).forEach(articles => {
    count += articles.filter(article =>
      article.tags.some(tag => (tag.id || tag._id) == tagId)
    ).length
  })
  return count
}

// 修改标签点击处理函数
const handleTagClick = async (tagId: number | string) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
  
  // 如果当前选中的文章不包含选中的标签，清空选中的文章
  if (selectedArticle.value) {
    const articleTags = selectedArticle.value.tags.map(tag => tag._id)
    const hasSelectedTags = selectedTags.value.every(tagId => 
      articleTags.includes(tagId)
    )
    if (!hasSelectedTags) {
      selectedArticle.value = null
    }
  }
}

// 处理文章选择
const handleArticleSelect = (article: any) => {
  console.log('选择文章:', article)
  selectedArticle.value = article
  // 更新 URL，但不触发路由变化
  const query = { ...route.query, id: article._id }
  router.replace({ query })
}

// 修改初始化函数
const initializeData = async () => {
  try {
    // 1. 先获取分类和标签
    await Promise.all([
      fetchCategories(),
      fetchTags()
    ])

    // 2. 如果URL中有文章ID，加载文章
    const articleId = route.query.id
    if (articleId) {
      const res = await articleApi.getArticle(articleId as string)
      if (res.data?.code === 0 || res.data?.code === 200) {
        selectedArticle.value = res.data.data
        // 3. 如果文章有分类，加载该分类的文章
        if (selectedArticle.value.category) {
          const categoryId = selectedArticle.value.category.id
          await fetchCategoryArticles(categoryId)
        }
        console.log('选中的文章:', selectedArticle.value)
      }
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    message.error('初始化数据失败')
  }
}

// 修改折叠面板的处理函数
const handleCollapseChange = async (activeNames: string | string[]) => {
  if (typeof activeNames === 'string') {
    const categoryId = parseInt(activeNames)
    if (!categoryArticles.value[categoryId]) {
      // 延迟100ms再加载文章，避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 100))
      await fetchCategoryArticles(categoryId)
    }
  }
}

// 修改监听路由参数的逻辑
watch(
  () => route.query.id,
  async (id) => {
    if (id) {
      try {
        const res = await articleApi.getArticle(id as string)
        if (res.data?.code === 0 || res.data?.code === 200) {
          selectedArticle.value = res.data.data
          // 如果文章有分类且该分类的文章未加载，则加载
          if (selectedArticle.value.category) {
            const categoryId = selectedArticle.value.category.id
            if (!categoryArticles.value[categoryId]) {
              // 延迟100ms再加载文章，避免请求过于频繁
              await new Promise(resolve => setTimeout(resolve, 100))
              await fetchCategoryArticles(categoryId)
            }
          }
        }
      } catch (error) {
        console.error('获取文章详情失败:', error)
        message.error('获取文章详情失败')
      }
    }
  }
)

onMounted(() => {
  initializeData()
})

const filterArticles = (article: Article) => {
  if (selectedTags.value.length === 0) return true
  return article.tags?.some(tag => selectedTags.value.includes(tag.id)) || false
}

const toggleTag = (tagId: number) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index === -1) {
    selectedTags.value.push(tagId)
  } else {
    selectedTags.value.splice(index, 1)
  }
}
</script>

<style lang="scss" scoped>
.articles-page {
  min-height: 100vh;
  background: var(--bg-color);

  // 页面标题样式
  .page-header {
    background: linear-gradient(135deg, #00B4DB 0%, #0083B0 100%);
    padding: 48px 0 32px;
    border-bottom: none;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
      opacity: 0.6;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 32px;
      text-align: center;
      position: relative;
      z-index: 1;
    }

    .page-title {
      margin: 0 0 12px;
      font-size: 36px;
      font-weight: 700;
      letter-spacing: -0.5px;
      color: white;

      :deep(.n-gradient-text) {
        background-image: linear-gradient(to right, #ffffff, #e0f7fa);
        -webkit-background-clip: text;
      }
    }

    .page-desc {
      margin: 0;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.9);
      opacity: 0.9;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }

  // 标签区域样式
  .tags-section {
    background: var(--card-bg);
    padding: 16px 0;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: saturate(180%) blur(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 32px;
    }

    .tags-wrapper {
      display: flex;
      align-items: center;
      gap: 16px;

      .tags-label {
        color: var(--text-color);
        font-weight: 500;
        font-size: 15px;
        white-space: nowrap;
      }

      :deep(.n-tag) {
        padding: 6px 16px;
        font-size: 14px;
        transition: all 0.3s ease;

        .n-icon {
          margin-right: 4px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 主要内容区域
  .content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px 32px;

    // 左侧导航卡片
    .nav-card {
      background: var(--card-bg);
      border-radius: 12px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .nav-header {
        padding: 16px 20px;
        border-bottom: 1px solid var(--border-color);
      }

      :deep(.custom-collapse) {
        background: transparent;
        border: none;

        .n-collapse-item {
          .n-collapse-item__header {
            padding: 12px 20px;
            transition: all 0.3s ease;
            
            &:hover {
              background: var(--hover-color);
            }

            .collapse-header {
              display: flex;
              align-items: center;
              gap: 8px;
              color: var(--text-color);
              font-weight: 500;
              font-size: 15px;

              .n-icon {
                color: var(--n-primary-color);
              }

              .article-count {
                font-size: 13px;
                color: var(--text-color);
                opacity: 0.5;
                margin-left: 4px;
              }
            }
          }
        }
      }

      .nav-articles {
        .nav-item {
          padding: 10px 20px 10px 48px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          .nav-item-content {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-color);
            font-size: 14px;
            opacity: 0.85;

            .n-icon {
              color: var(--text-color);
              opacity: 0.5;
              flex-shrink: 0;
            }

            .nav-item-title {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          
          &:hover {
            background: var(--hover-color);
            .nav-item-content {
              opacity: 1;
              color: var(--n-primary-color);
              .n-icon {
                color: var(--n-primary-color);
                opacity: 1;
              }
            }
          }
          
          &.active {
            background: var(--hover-color);
            .nav-item-content {
              opacity: 1;
              color: var(--n-primary-color);
              font-weight: 500;
              .n-icon {
                color: var(--n-primary-color);
                opacity: 1;
              }
            }
          }
        }

        .empty-message {
          padding: 12px 20px 12px 48px;
          color: var(--text-color);
          opacity: 0.5;
          font-size: 14px;
          font-style: italic;
        }
      }
    }

    // 右侧内容卡片
    .content-card {
      background: var(--card-bg);
      border-radius: 12px;
      min-height: 600px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .article-content {
        padding: 24px 32px;

        .article-header {
          margin-bottom: 32px;
          padding-bottom: 24px;
          border-bottom: 1px solid var(--border-color);

          .article-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-color);
            margin: 0 0 24px;
            line-height: 1.4;
            letter-spacing: -0.5px;
          }

          .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .meta-left {
              display: flex;
              align-items: center;
              gap: 12px;

              .author-info {
                .author-name {
                  font-size: 16px;
                  font-weight: 500;
                  color: var(--text-color);
                  margin-bottom: 4px;
                }

                .post-info {
                  font-size: 14px;
                  color: var(--text-color);
                  opacity: 0.6;

                  .dot {
                    margin: 0 8px;
                  }
                }
              }
            }

            .meta-right {
              :deep(.n-tag) {
                font-size: 12px;
              }
            }
          }
        }

        .article-cover {
          margin: -24px -32px 32px;
          height: 300px;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.02);
            }
          }
        }

        .article-body {
          color: var(--text-color);
          font-size: 16px;
          line-height: 1.8;
          letter-spacing: 0.2px;

          :deep(.rich-content) {
            background: transparent;
            padding: 0;
            
            img {
              max-width: 100%;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              margin: 1rem 0;
            }
            
            video {
              max-width: 100%;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              margin: 1rem 0;
              background-color: #000;
            }
            
            h1, h2, h3, h4, h5, h6 {
              margin: 1.5rem 0 1rem;
              color: var(--text-color);
              font-weight: 600;
            }
            
            a {
              color: var(--n-primary-color);
              text-decoration: none;
              border-bottom: 1px solid var(--n-primary-color);
              transition: all 0.3s ease;
              
              &:hover {
                opacity: 0.8;
              }
            }
            
            blockquote {
              border-left: 4px solid var(--n-primary-color);
              padding: 0.5rem 1rem;
              margin: 1rem 0;
              background-color: var(--hover-color);
              color: var(--text-color);
              font-style: italic;
            }
            
            code {
              background-color: rgba(0, 0, 0, 0.05);
              padding: 0.2em 0.4em;
              border-radius: 3px;
              font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
              font-size: 85%;
              color: #476582;
            }
            
            pre {
              background-color: #282c34;
              color: #abb2bf;
              border-radius: 6px;
              padding: 16px;
              margin: 16px 0;
              overflow-x: auto;
              font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
              font-size: 14px;
              line-height: 1.5;
              box-shadow: none;
              
              code {
                background-color: transparent;
                padding: 0;
                color: inherit;
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
              }
            }
            
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 1rem 0;
              
              th, td {
                border: 1px solid var(--border-color);
                padding: 0.5rem;
              }
              
              th {
                background-color: var(--hover-color);
                font-weight: 600;
              }
              
              tr:nth-child(even) {
                background-color: var(--hover-color);
                opacity: 0.8;
              }
            }
            
            ul, ol {
              margin: 1rem 0;
              padding-left: 2rem;
              
              li {
                margin-bottom: 0.5rem;
              }
            }
          }
        }
      }
    }

    // 空状态
    .empty-state {
      min-height: 600px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--text-color-disabled);
      
      :deep(.n-empty-description) {
        font-size: 16px;
        margin-top: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .articles-page {
    .page-header {
      padding: 32px 0 24px;

      .container {
        padding: 0 16px;
      }

      .page-title {
        font-size: 28px;
      }
    }

    .tags-section {
      .container {
        padding: 0 16px;
      }

      .tags-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .tags-label {
          margin-bottom: 4px;
        }
      }
    }

    .content-wrapper {
      padding: 16px;

      :deep(.n-grid) {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .n-grid-item {
          width: 100% !important;
        }
      }

      .content-card {
        .article-content {
          padding: 20px;

          .article-header {
            .article-title {
              font-size: 24px;
              margin-bottom: 20px;
            }

            .article-meta {
              flex-direction: column;
              align-items: flex-start;
              gap: 12px;

              .meta-tags {
                margin-top: 8px;
              }
            }
          }

          .article-cover {
            margin: -20px -20px 24px;
            height: 200px;
          }
        }
      }
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .nav-card, .content-card {
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
  }
}
</style> 