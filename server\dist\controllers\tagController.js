"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTag = exports.updateTag = exports.createTag = exports.getTag = exports.getTags = void 0;
const Tag_1 = __importDefault(require("../models/Tag"));
const Article_1 = __importDefault(require("../models/Article"));
// @desc    获取所有标签
// @route   GET /api/tags
// @access  Public
const getTags = async (req, res) => {
    try {
        // 获取所有标签
        const tags = await Tag_1.default.find()
            .sort('name');

        // 获取每个标签的文章数量
        const tagsWithCount = await Promise.all(
            tags.map(async (tag) => {
                const count = await Article_1.default.countDocuments({
                    tags: tag._id,
                    status: 'published' // 只统计已发布的文章
                });

                return {
                    ...tag.toObject(),
                    article_count: count
                };
            })
        );

        res.json({
            code: 0,
            message: '获取标签列表成功',
            data: tagsWithCount
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '获取标签列表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getTags = getTags;
// @desc    获取单个标签
// @route   GET /api/tags/:id
// @access  Public
const getTag = async (req, res) => {
    try {
        const { id } = req.params;
        const tag = await Tag_1.default.findById(id);
        if (!tag) {
            return res.status(404).json({
                code: 404,
                message: '标签不存在'
            });
        }
        res.json({
            code: 0,
            message: '获取标签成功',
            data: tag
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '获取标签失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getTag = getTag;
// @desc    创建标签
// @route   POST /api/tags
// @access  Private/Admin
const createTag = async (req, res) => {
    try {
        const tagData = req.body;
        const tag = await Tag_1.default.create(tagData);
        res.status(201).json({
            code: 0,
            message: '创建标签成功',
            data: tag
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '创建标签失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.createTag = createTag;
// @desc    更新标签
// @route   PUT /api/tags/:id
// @access  Private/Admin
const updateTag = async (req, res) => {
    try {
        const { id } = req.params;
        const tag = await Tag_1.default.findById(id);
        if (!tag) {
            return res.status(404).json({
                code: 404,
                message: '标签不存在'
            });
        }
        const updatedTag = await Tag_1.default.findByIdAndUpdate(id, { $set: req.body }, { new: true });
        res.json({
            code: 0,
            message: '更新标签成功',
            data: updatedTag
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '更新标签失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.updateTag = updateTag;
// @desc    删除标签
// @route   DELETE /api/tags/:id
// @access  Private/Admin
const deleteTag = async (req, res) => {
    try {
        const { id } = req.params;
        const tag = await Tag_1.default.findById(id);
        if (!tag) {
            return res.status(404).json({
                code: 404,
                message: '标签不存在'
            });
        }
        await tag.deleteOne();
        res.json({
            code: 0,
            message: '删除标签成功'
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '删除标签失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.deleteTag = deleteTag;
