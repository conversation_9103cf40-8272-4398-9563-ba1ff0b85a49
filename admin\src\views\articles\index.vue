<template>
  <div class="articles-page">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-title">
        <FileTextOutlined /> 文章管理
      </div>
      <div class="header-actions">
        <router-link to="/articles/create">
          <a-button type="primary">
            <template #icon><PlusOutlined /></template>
            写文章
          </a-button>
        </router-link>
      </div>
    </div>

    <!-- 筛选表单 -->
    <a-card :bordered="false" class="filter-card">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="状态">
          <a-select
            v-model:value="filterForm.status"
            style="width: 120px"
            placeholder="选择状态"
            allowClear
            @change="handleFilter"
          >
            <a-select-option value="published">已发布</a-select-option>
            <a-select-option value="draft">草稿</a-select-option>
            <a-select-option value="archived">已归档</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="分类">
          <a-select
            v-model:value="filterForm.category"
            style="width: 160px"
            placeholder="选择分类"
            allowClear
            @change="handleFilter"
          >
            <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标签">
          <a-select
            v-model:value="filterForm.tag"
            style="width: 160px"
            placeholder="选择标签"
            allowClear
            @change="handleFilter"
          >
            <a-select-option v-for="tag in tags" :key="tag.id" :value="tag.id">
              {{ tag.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 文章列表 -->
    <a-card :bordered="false" class="list-card">
      <a-table
        :columns="columns"
        :data-source="articles"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 标题列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <div class="article-title-cell">
              <router-link :to="`/articles/edit/${record.id}`">{{ record.title }}</router-link>
              <a-tag :color="getStatusColor(record.status)" class="status-tag">
                {{ getStatusText(record.status) }}
              </a-tag>
            </div>
          </template>

          <!-- 分类列 -->
          <template v-if="column.key === 'category'">
            <a-tag v-if="record.category?.name" color="blue">{{ record.category.name }}</a-tag>
          </template>

          <!-- 标签列 -->
          <template v-if="column.key === 'tags'">
            <a-space wrap>
              <a-tag v-for="tag in record.tags" :key="tag._id" color="purple">
                {{ tag.name }}
              </a-tag>
            </a-space>
          </template>

          <!-- 作者列 -->
          <template v-if="column.key === 'author'">
            <a-space>
              <a-avatar :style="{ backgroundColor: '#1890ff' }" size="small">
                {{ record.author?.username?.charAt(0)?.toUpperCase() }}
              </a-avatar>
              <span class="author-name">{{ record.author?.username }}</span>
            </a-space>
          </template>

          <!-- 创建时间列 -->
          <template v-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>

          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <router-link :to="`/articles/edit/${record.id}`">
                <a-button type="link" size="small">
                  <template #icon><EditOutlined /></template>
                  编辑
                </a-button>
              </router-link>
              <a-popconfirm
                title="确定要删除这篇文章吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record.id)"
              >
                <a-button type="link" danger size="small">
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import {
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import articleApi from '@/api/modules/article'
import categoryApi from '@/api/modules/category'
import tagApi from '@/api/modules/tag'
import type { Article } from '@/api/modules/article'
import { formatDate } from '@/utils/format'

// 状态
const loading = ref(false)
const articles = ref<Article[]>([])
const categories = ref<any[]>([])
const tags = ref<any[]>([])
const pagination = ref<TablePaginationConfig>({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

// 定义表格列
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: '30%'
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    width: '15%'
  },
  {
    title: '标签',
    dataIndex: 'tags',
    key: 'tags',
    width: '15%'
  },
  {
    title: '作者',
    dataIndex: 'author',
    key: 'author',
    width: '15%'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: '15%'
  },
  {
    title: '操作',
    key: 'action',
    width: '10%'
  }
]

// 筛选表单
const filterForm = ref({
  status: undefined,
  category: undefined,
  tag: undefined
})

// 获取文章状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    published: 'success',
    draft: 'warning',
    archived: 'default'
  }
  return colors[status as keyof typeof colors]
}

// 获取文章状态文本
const getStatusText = (status: string) => {
  const texts = {
    published: '已发布',
    draft: '草稿',
    archived: '已归档'
  }
  return texts[status as keyof typeof texts]
}

// 加载文章列表
const fetchArticles = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.current || 1,
      pageSize: pagination.value.pageSize || 10,
      ...filterForm.value
    }

    // 添加详细的用户信息日志
    const token = localStorage.getItem('token')
    const userStr = localStorage.getItem('user')
    let userInfo = null
    try {
      userInfo = JSON.parse(userStr || '{}')
    } catch (e) {
      console.error('解析用户信息失败:', e)
    }

    console.log('发送请求前的信息:', {
      token: token ? '存在' : '不存在',
      tokenValue: token,
      user: userInfo,
      role: userInfo?.role,
      params
    })

    const res = await articleApi.getArticles(params)
    console.log('文章列表响应:', res)

    // 兼容不同的响应格式
    if (res.data?.code === 0 || res.data?.code === 200) {
      const responseData = res.data.data
      console.log('文章数据:', {
        total: responseData.total,
        currentPage: responseData.page,
        pageSize: responseData.pageSize,
        itemsCount: responseData.items?.length,
        items: responseData.items?.map(item => ({
          id: item.id,
          title: item.title,
          status: item.status,
          visibility: item.visibility
        }))
      })

      articles.value = responseData.items || []
      pagination.value.total = responseData.total || 0
    } else if (res.data) {
      // 兼容旧格式
      console.log('使用旧格式数据:', {
        total: res.data.total,
        itemsCount: res.data.items?.length
      })
      articles.value = res.data.items || []
      pagination.value.total = res.data.total || 0
    } else {
      console.warn('响应数据格式异常:', res)
      articles.value = []
      pagination.value.total = 0
    }
  } catch (error: any) {
    console.error('获取文章列表失败:', error)
    message.error(error.response?.data?.message || '获取文章列表失败')
  } finally {
    loading.value = false
  }
}

// 加载分类列表
const fetchCategories = async () => {
  try {
    const res = await categoryApi.getCategories()
    categories.value = res.data || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 加载标签列表
const fetchTags = async () => {
  try {
    const res = await tagApi.getTags()
    tags.value = res.data || []
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}

// 处理表格变化
const handleTableChange = (pag: TablePaginationConfig) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchArticles()
}

// 处理筛选
const handleFilter = () => {
  pagination.value.current = 1
  fetchArticles()
}

// 处理删除
const handleDelete = async (id: number) => {
  if (!id) {
    message.error('无效的文章ID')
    return
  }

  try {
    await articleApi.deleteArticle(id.toString())
    message.success('删除成功')
    fetchArticles()
  } catch (error: any) {
    console.error('删除文章失败:', error)
    message.error(error.response?.data?.message || '删除文章失败')
  }
}

onMounted(() => {
  fetchArticles()
  fetchCategories()
  fetchTags()
})
</script>

<style scoped>
.articles-page {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-title {
  font-size: 20px;
  font-weight: 500;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.list-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.article-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.article-title-cell a {
  color: #262626;
  text-decoration: none;
}

.article-title-cell a:hover {
  color: #1890ff;
}

.status-tag {
  margin-left: 8px;
}

.author-name {
  margin-left: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background-color: #fafafa;
}
</style>
